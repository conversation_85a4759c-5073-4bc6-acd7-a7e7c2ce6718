// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        google()
        mavenCentral()
        maven {
            url("https://jitpack.io")
            content {
                includeGroup("com.github.aasitnikov")
            }
        }
    }
    dependencies {
        classpath "com.android.tools.build:gradle:8.7.0"
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.22'

        classpath 'com.github.aasitnikov:fat-aar-android:1.4.1'
//        classpath 'com.github.kezong:fat-aar:1.3.6'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    project.ext {
        versionName = "1.7.17"
        facetecVersion = "9.7.68"
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}