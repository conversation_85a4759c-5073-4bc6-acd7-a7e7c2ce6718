package com.scb.techx.ekycframework.ui.processor

import android.util.Base64
import androidx.annotation.Keep

@Keep
class Config {
    companion object {

        var identifierType = ""
        var identifierValue = ""
        var serviceId = ""
        var token = ""
        var sessionId = ""
        var x_session_id = ""
        var isNewEkycToken = false
        var checkDopa = false

        var baseUrl = ""

        const val PROJECT_VERSION = "1.7.17"

        private fun decode(str: String): String {
            return String(Base64.decode(str, Base64.DEFAULT))
        }

        object OcrEndpoint {
            val CONFIRM_INFO_ENDPOINT = decode("L3YxL2VreWMvY29uZmlybWF0aW9uLWluZm8=") // /v1/ekyc/confirmation-info
            val MATCH_3D_2D_IDSCAN_FRONT_ENDPOINT = decode("L3YxL2VreWMvbWF0Y2gtM2QtMmQtaWRzY2FuL2Zyb250") // /v1/ekyc/match-3d-2d-idscan/front
            val MATCH_3D_2D_IDSCAN_BACK_ENDPOINT = decode("L3YxL2VreWMvbWF0Y2gtM2QtMmQtaWRzY2FuL2JhY2s=") // /v1/ekyc/match-3d-2d-idscan/back
            val ID_SCAN_ONLY_ENDPOINT = decode("L3YxL2VreWMvaWRzY2FuLW9ubHk=") // /v1/ekyc/idscan-only
        }

        object LivenessEndpoint {
            val ENROLLMENT_3D_ENDPOINT = decode("L3YxL2VreWMvZW5yb2xsbWVudC0zZA==")
        }

        object NdidEndpoint {
            val NDID_STATUS_ENDPOINT = decode("L3YxL2VreWMvbmRpZC9zdGF0dXM=")
            val NDID_REQUEST_CANCEL_ENDPOINT = decode("L3YxL2VreWMvbmRpZC9yZXF1ZXN0L2NhbmNlbA==")
            val NDID_IDP_ENDPOINT = decode("L3YxL2VreWMvbmRpZC9pZHA=")
            val NDID_REQUEST_ENDPOINT = decode("L3YxL2VreWMvbmRpZC9yZXF1ZXN0")
        }

        object GetSessionEndpoint {
            val GET_SESSION_ENDPOINT = decode("L3YxL2VreWMvYXV0aGVuL3Nlc3Npb250b2tlbg==")
            val GET_SESSION_FACETEC_ENDPOINT = decode("L3YxL2VreWMvYXV0aGVuL3Nlc3Npb250b2tlbi9mYWNldGVj")
        }

        object InitFlowEndpoint {
            val INIT_FLOW_ENDPOINT = decode("L2VreWMvaW5pdC1mbG93")
        }
    }
}