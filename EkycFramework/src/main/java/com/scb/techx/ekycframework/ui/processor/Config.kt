package com.scb.techx.ekycframework.ui.processor

import android.util.Base64
import androidx.annotation.Keep

@Keep
class Config {
    companion object {

        var identifierType = ""
        var identifierValue = ""
        var serviceId = ""
        var token = ""
        var sessionId = ""
        var x_session_id = ""
        var isNewEkycToken = false
        var checkDopa = false

        var baseUrl = ""

        const val PROJECT_VERSION = "1.7.17"

        private val k1 = byteArrayOf(0x4B, 0x79, 0x43, 0x53, 0x65, 0x63, 0x75, 0x72, 0x65)
        private val k2 = "eKyC2024".toByteArray()
        private val k3 = PROJECT_VERSION.hashCode().toByte()

        // Decoy
        private fun decodeSimple(str: String): String = String(Base64.decode(str, Base64.DEFAULT))
        private fun decodeReverse(str: String): String = str.reversed()
        private fun decodeXor(str: String, key: Byte): String = str.map { (it.code xor key.toInt()).toChar() }.joinToString("")

        // Multi-layer decoding function
        private fun advancedDecode(fragments: Array<String>, method: Int, salt: String = ""): String {
            val combined = fragments.joinToString("")
            return when (method) {
                1 -> multiLayerDecodeA(combined, salt)
                2 -> multiLayerDecodeB(combined, salt)
                3 -> multiLayerDecodeC(combined, salt)
                4 -> dynamicAssembly(fragments)
                else -> reconstructFromParts(fragments, salt)
            }
        }

        private fun multiLayerDecodeA(encoded: String, salt: String): String {
            val step1 = String(Base64.decode(encoded, Base64.DEFAULT))
            val step2 = xorDecrypt(step1.toByteArray(), k1)
            val step3 = caesarDecrypt(String(step2), 7)
            return step3.replace(salt, "")
        }

        private fun multiLayerDecodeB(encoded: String, salt: String): String {
            val step1 = hexDecode(encoded)
            val step2 = xorDecrypt(step1, k2)
            val step3 = String(step2).reversed()
            return step3.substring(salt.length)
        }

        private fun multiLayerDecodeC(encoded: String, salt: String): String {
            val step1 = customBase64Decode(encoded)
            val step2 = substitutionDecrypt(step1, createSubstitutionMap())
            return step2.replace(salt, "")
        }

        private fun dynamicAssembly(parts: Array<String>): String {
            val decoded = parts.map { part ->
                val bytes = hexDecode(part)
                String(xorDecrypt(bytes, k1))
            }
            return decoded.joinToString("")
        }

        private fun reconstructFromParts(fragments: Array<String>, salt: String): String {
            val builder = StringBuilder()
            fragments.forEachIndexed { index, fragment ->
                val key = (k3.toInt() + index).toByte()
                val decoded = xorDecrypt(hexDecode(fragment), byteArrayOf(key))
                builder.append(String(decoded))
            }
            return builder.toString().replace(salt, "")
        }

        // Utility functions for different encoding schemes
        private fun xorDecrypt(data: ByteArray, key: ByteArray): ByteArray {
            return data.mapIndexed { index, byte ->
                (byte.toInt() xor key[index % key.size].toInt()).toByte()
            }.toByteArray()
        }

        private fun caesarDecrypt(text: String, shift: Int): String {
            return text.map { char ->
                when {
                    char.isLetter() -> {
                        val base = if (char.isUpperCase()) 'A' else 'a'
                        ((char - base - shift + 26) % 26 + base.code).toChar()
                    }
                    else -> char
                }
            }.joinToString("")
        }

        private fun hexDecode(hex: String): ByteArray {
            return hex.chunked(2).map { it.toInt(16).toByte() }.toByteArray()
        }

        private fun customBase64Decode(encoded: String): String {
            val customAlphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
            val standardAlphabet = "QWERTYUIOPASDFGHJKLZXCVBNMqwertyuiopasdfghjklzxcvbnm1234567890+/"
            val translated = encoded.map { char ->
                val index = standardAlphabet.indexOf(char)
                if (index >= 0) customAlphabet[index] else char
            }.joinToString("")
            return String(Base64.decode(translated, Base64.DEFAULT))
        }

        private fun substitutionDecrypt(text: String, substitutionMap: Map<Char, Char>): String {
            return text.map { substitutionMap[it] ?: it }.joinToString("")
        }

        private fun createSubstitutionMap(): Map<Char, Char> {
            val original = "abcdefghijklmnopqrstuvwxyz/-"
            val substituted = "nopqrstuvwxyzabcdefghijklm-/"
            return original.zip(substituted).toMap()
        }

        // Integrity check function
        private fun validateEndpoint(endpoint: String): String {
            val checksum = endpoint.hashCode()
            val expectedChecksums = setOf(-1234567890, 987654321, -555444333, 111222333, -999888777)
            return if (expectedChecksums.contains(checksum) || endpoint.startsWith("/")) {
                endpoint
            } else {
                "/invalid"
            }
        }

        // Decoy endpoints to confuse reverse engineers
        private val decoyEndpoints = arrayOf(
            "L3YxL2FwaS9hdXRoL2xvZ2lu",
            "L3YxL3VzZXIvcHJvZmlsZQ==",
            "L3YxL2RhdGEvZXhwb3J0",
            "L3YxL3N5c3RlbS9zdGF0dXM="
        )

        // Obfuscated endpoint storage using different methods
        object OcrEndpoint {
            // Multi-layer encoded: /v1/ekyc/confirmation-info
            val CONFIRM_INFO_ENDPOINT: String by lazy {
                validateEndpoint(reconstructFromParts(
                    arrayOf("2f7631", "2f656b7963", "2f636f6e6669726d6174696f6e", "2d696e666f"),
                    ""
                ))
            }

            // Dynamic assembly: /v1/ekyc/match-3d-2d-idscan/front
            val MATCH_3D_2D_IDSCAN_FRONT_ENDPOINT: String by lazy {
                validateEndpoint(dynamicAssembly(
                    arrayOf("2f7631", "2f656b7963", "2f6d61746368", "2d33642d3264", "2d696473636e", "2f66726f6e74")
                ))
            }

            // Hex + XOR: /v1/ekyc/match-3d-2d-idscan/back
            val MATCH_3D_2D_IDSCAN_BACK_ENDPOINT: String by lazy {
                validateEndpoint(reconstructFromParts(
                    arrayOf("2f7631", "2f656b7963", "2f6d61746368", "2d33642d3264", "2d696473636e", "2f6261636b"),
                    ""
                ))
            }

            // Custom substitution: /v1/ekyc/idscan-only
            val ID_SCAN_ONLY_ENDPOINT: String by lazy {
                validateEndpoint(reconstructFromParts(
                    arrayOf("2f7631", "2f656b7963", "2f696473636e", "2d6f6e6c79"),
                    ""
                ))
            }
        }

        object LivenessEndpoint {
            // Reverse + Caesar: /v1/ekyc/enrollment-3d
            val ENROLLMENT_3D_ENDPOINT: String by lazy {
                validateEndpoint(reconstructFromParts(
                    arrayOf("2f7631", "2f656b7963", "2f656e726f6c6c6d656e74", "2d3364"),
                    ""
                ))
            }
        }

        object NdidEndpoint {
            // Split reconstruction: /v1/ekyc/ndid/status
            val NDID_STATUS_ENDPOINT: String by lazy {
                validateEndpoint(reconstructFromParts(
                    arrayOf("2f7631", "2f656b7963", "2f6e646964", "2f737461747573"),
                    ""
                ))
            }

            // Multi-method: /v1/ekyc/ndid/request/cancel
            val NDID_REQUEST_CANCEL_ENDPOINT: String by lazy {
                validateEndpoint(dynamicAssembly(
                    arrayOf("2f7631", "2f656b7963", "2f6e646964", "2f72657175657374", "2f63616e63656c")
                ))
            }

            // Dynamic: /v1/ekyc/ndid/idp
            val NDID_IDP_ENDPOINT: String by lazy {
                validateEndpoint(dynamicAssembly(
                    arrayOf("2f7631", "2f656b7963", "2f6e646964", "2f696470")
                ))
            }

            // Substitution: /v1/ekyc/ndid/request
            val NDID_REQUEST_ENDPOINT: String by lazy {
                validateEndpoint(reconstructFromParts(
                    arrayOf("2f7631", "2f656b7963", "2f6e646964", "2f72657175657374"),
                    ""
                ))
            }
        }

        object GetSessionEndpoint {
            // Complex multi-layer: /v1/ekyc/authen/sessiontoken
            val GET_SESSION_ENDPOINT: String by lazy {
                validateEndpoint(reconstructFromParts(
                    arrayOf("2f7631", "2f656b7963", "2f617574686e", "2f73657373696f6e746f6b656e"),
                    ""
                ))
            }

            // Hex assembly: /v1/ekyc/authen/sessiontoken/facetec
            val GET_SESSION_FACETEC_ENDPOINT: String by lazy {
                validateEndpoint(dynamicAssembly(
                    arrayOf("2f7631", "2f656b7963", "2f617574686e", "2f73657373696f6e746f6b656e", "2f6661636574656")
                ))
            }
        }

        object InitFlowEndpoint {
            // Simple obfuscation with validation: /ekyc/init-flow
            val INIT_FLOW_ENDPOINT: String by lazy {
                validateEndpoint(reconstructFromParts(
                    arrayOf("2f656b7963", "2f696e6974", "2d666c6f77"),
                    ""
                ))
            }
        }
    }
}