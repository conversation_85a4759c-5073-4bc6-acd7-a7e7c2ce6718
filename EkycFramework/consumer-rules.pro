# eKYC Framework Consumer ProGuard Rules
# These rules will be automatically applied when this library is consumed
# The library itself is now obfuscated, so these rules ensure proper integration

# ===== PUBLIC API PRESERVATION =====
# Keep the main entry point that apps use
-keep public class com.scb.techx.ekycframework.util.EkycUtilities {
    public *;
}

# Keep all callback interfaces that apps implement
-keep public interface com.scb.techx.ekycframework.util.EkycUtilities$* {
    public *;
}

# Keep configuration classes that apps access
-keep public class com.scb.techx.ekycframework.ui.processor.Config {
    public *;
}
-keep public class com.scb.techx.ekycframework.ui.processor.Config$* {
    public *;
}

-keep public class com.scb.techx.ekycframework.HandleCallback {
    public *;
}

-keep public class com.scb.techx.ekycframework.Constants {
    public *;
}
-keep public class com.scb.techx.ekycframework.Constants$* {
    public *;
}

# Keep theme customization classes
-keep public class com.scb.techx.ekycframework.ui.theme.** {
    public *;
}

# ===== RETROFIT & RXJAVA INTEGRATION =====
# Even though the library is obfuscated, we need these for proper integration

# Keep Retrofit and RxJava integration classes
-keep class retrofit2.adapter.rxjava3.** { *; }
-dontwarn retrofit2.adapter.rxjava3.**

# Keep essential Retrofit classes
-keep class retrofit2.** { *; }
-keep,allowobfuscation,allowshrinking class retrofit2.Call
-keep,allowobfuscation,allowshrinking class retrofit2.Response

# Keep RxJava classes for proper integration
-keep class io.reactivex.rxjava3.** { *; }
-keep,allowobfuscation,allowshrinking class io.reactivex.rxjava3.core.Single
-keep,allowobfuscation,allowshrinking class io.reactivex.rxjava3.core.Observable
-keep,allowobfuscation,allowshrinking class io.reactivex.rxjava3.core.Completable

# ===== GSON SERIALIZATION =====
# Keep Gson classes and annotations
-keep class com.google.gson.** { *; }
-keep class com.google.gson.reflect.TypeToken { *; }
-keep class * extends com.google.gson.reflect.TypeToken

# Keep serialized field names (critical for API communication)
-keepclassmembers,allowobfuscation class * {
    @com.google.gson.annotations.SerializedName <fields>;
    @com.google.gson.annotations.Expose <fields>;
}

# ===== PRESERVE SIGNATURES =====
# Critical for generic type preservation and reflection
-keepattributes Signature,RuntimeVisibleAnnotations,AnnotationDefault,InnerClasses,EnclosingMethod

# ===== FRAMEWORK INTEGRATION =====
# Keep classes that apps might access through reflection or callbacks
-keep @androidx.annotation.Keep class * {
    public *;
}
-keepclassmembers class * {
    @androidx.annotation.Keep *;
}

# Keep classes that extend HashMap (like AuthenticatedHeaders)
-keep class * extends java.util.HashMap {
    public *;
}

# ===== THIRD-PARTY LIBRARIES =====
# FaceTec SDK (if used by consuming apps)
-dontwarn com.facetec.sdk.**
-keep class com.facetec.sdk.** { *; }

# OkHttp
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**

# ===== ANDROID FRAMEWORK =====
# Keep enum classes
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep Parcelable implementations
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Keep classes with native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# ===== KOTLIN SUPPORT =====
# Keep Kotlin metadata for proper function signatures
-keep class kotlin.Metadata { *; }
-keepclassmembers class * {
    @kotlin.jvm.JvmStatic *;
}