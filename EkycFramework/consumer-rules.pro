# eKYC Framework Consumer ProGuard Rules
# These rules will be automatically applied when this library is consumed

# ===== RETROFIT =====
# Keep Retrofit interfaces and their methods
-keep interface com.scb.techx.ekycframework.data.**.api.** { *; }
-keep class com.scb.techx.ekycframework.data.**.api.** { *; }

# Keep Retrofit service method signatures and annotations
-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}

# Keep generic signature of Call, Response (R8 full mode strips signatures from non-kept items)
-keep,allowobfuscation,allowshrinking class retrofit2.Call
-keep,allowobfuscation,allowshrinking class retrofit2.Response

# With R8 full mode generic signatures are stripped for classes that are not kept
-keep,allowobfuscation,allowshrinking class kotlin.coroutines.Continuation

# ===== RXJAVA =====
# Keep RxJava classes and their generic signatures
-keep class io.reactivex.rxjava3.** { *; }
-keep interface io.reactivex.rxjava3.** { *; }

# Keep RxJava Single, Observable, etc. with their generic information
-keep,allowobfuscation,allowshrinking class io.reactivex.rxjava3.core.Single
-keep,allowobfuscation,allowshrinking class io.reactivex.rxjava3.core.Observable
-keep,allowobfuscation,allowshrinking class io.reactivex.rxjava3.core.Completable

# ===== GSON =====
# Keep Gson annotations and serialized names
-keepattributes Signature
-keepattributes *Annotation*
-keepattributes EnclosingMethod

# Keep classes with @SerializedName annotations
-keepclassmembers,allowobfuscation class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# ===== EKYC FRAMEWORK SPECIFIC =====
# Keep all API response/request entities with their generic signatures
-keep class com.scb.techx.ekycframework.data.**.model.** { *; }
-keep class com.scb.techx.ekycframework.domain.**.model.** { *; }

# Keep repository implementations and their method signatures
-keep class com.scb.techx.ekycframework.data.**.datarepository.** { *; }
-keep interface com.scb.techx.ekycframework.domain.**.repository.** { *; }

# Keep mapper classes
-keep class com.scb.techx.ekycframework.data.**.mapper.** { *; }

# Keep API header classes
-keep class com.scb.techx.ekycframework.domain.apihelper.** { *; }

# Keep classes annotated with @Keep
-keep @androidx.annotation.Keep class * { *; }
-keepclassmembers class * {
    @androidx.annotation.Keep *;
}

# ===== GENERIC TYPE PRESERVATION =====
# Preserve generic signatures for API interfaces and their return types
-keepattributes Signature,InnerClasses,EnclosingMethod

# Keep parameterized types for Retrofit interfaces
-keepclassmembers interface com.scb.techx.ekycframework.data.**.api.** {
    io.reactivex.rxjava3.core.Single *(...);
    io.reactivex.rxjava3.core.Observable *(...);
    io.reactivex.rxjava3.core.Completable *(...);
}

# ===== OKHTTP =====
# Keep OkHttp classes that might be referenced
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**

# ===== ADDITIONAL SAFETY RULES =====
# Keep enum classes
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep Parcelable implementations
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}