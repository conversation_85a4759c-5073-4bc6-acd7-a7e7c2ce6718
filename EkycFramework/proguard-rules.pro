# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

-dontwarn javax.annotation.Nullable
-dontwarn com.facetec.sdk.**
-keep class com.facetec.sdk.**
{ *; }

-keepattributes InnerClasses

-keep class org.bouncycastle.** { *; }
-keepnames class org.bouncycastle.** { *; }

# Keep Conscrypt classes
-keep class org.conscrypt.** { *; }
-dontwarn org.conscrypt.**

# Keep OpenJSSE classes (if you are using it, less common than Conscrypt)
-keep class org.openjsse.** { *; }
-dontwarn org.openjsse.**

# Keep OkHttp's internal platform classes that might reference Conscrypt/OpenJSSE
-keep class okhttp3.internal.platform.** { *; }
-dontwarn okhttp3.internal.platform.**

-dontwarn org.bouncycastle.**

# ===== RETROFIT & RXJAVA INTEGRATION =====
# Essential rules for Retrofit + RxJava to work with obfuscation
-keep class retrofit2.adapter.rxjava3.** { *; }
-dontwarn retrofit2.adapter.rxjava3.**

# Keep Retrofit interfaces and annotations
-keepclasseswithmembers class * {
    @retrofit2.http.* <methods>;
}

# Keep RxJava classes
-keep class io.reactivex.rxjava3.** { *; }
-dontwarn io.reactivex.rxjava3.**

# ===== GSON SERIALIZATION =====
# Keep Gson classes and annotations
-keep class com.google.gson.** { *; }
-keep class com.google.gson.reflect.TypeToken { *; }
-keep class * extends com.google.gson.reflect.TypeToken

# Keep classes with @SerializedName
-keepclassmembers,allowobfuscation class * {
    @com.google.gson.annotations.SerializedName <fields>;
    @com.google.gson.annotations.Expose <fields>;
}

# ===== PRESERVE SIGNATURES =====
# Critical for generic type preservation
-keepattributes Signature,RuntimeVisibleAnnotations,AnnotationDefault

# ===== EKYC FRAMEWORK CLASSES =====
# Keep all API interfaces and their implementations
-keep interface com.scb.techx.ekycframework.data.**.api.** { *; }
-keep class com.scb.techx.ekycframework.data.**.datarepository.** { *; }

# Keep all model classes (request/response entities)
-keep class com.scb.techx.ekycframework.data.**.model.** { *; }
-keep class com.scb.techx.ekycframework.domain.**.model.** { *; }

# Keep mapper classes
-keep class com.scb.techx.ekycframework.data.**.mapper.** { *; }

# Keep classes that extend HashMap (AuthenticatedHeaders)
-keep class * extends java.util.HashMap { *; }

# Keep @Keep annotated classes and members
-keep @androidx.annotation.Keep class * { *; }
-keepclassmembers class * {
    @androidx.annotation.Keep *;
}