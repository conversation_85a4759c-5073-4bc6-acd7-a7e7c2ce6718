plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-parcelize'
}

android {
    namespace = "com.scb.techx.ekycproject"
    compileSdk 33

    buildFeatures {
        buildConfig true
    }
    defaultConfig {
        applicationId "com.scb.techx.ekycproject"
        minSdk 23
        targetSdk 35
        versionCode 1
        versionName project.ext.versionName

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.debug
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    packagingOptions {
        pickFirst 'lib/armeabi-v7a/libPhoenixAndroid.so'
        pickFirst 'lib/armeabi-v7a/libe9a3.so'
        pickFirst 'lib/arm64-v8a/libPhoenixAndroid.so'
        pickFirst 'lib/arm64-v8a/libe9a3.so'
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    flavorDimensions "default"
    productFlavors {
        prod {
            applicationIdSuffix ""
        }
        preprod {
            applicationIdSuffix ".sit"
        }
        uat {
            applicationIdSuffix ".uat"
        }

        sit {
            applicationIdSuffix ".sit"
        }
        dev {
            applicationIdSuffix ".dev"
        }
    }
}

dependencies {

    implementation 'androidx.core:core-ktx:1.7.0'
    implementation 'androidx.appcompat:appcompat:1.4.0'
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.2'
    implementation "org.jetbrains.kotlinx:kotlinx-serialization-json:1.3.0"
    implementation project(":EkycFramework")
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.espresso:espresso-contrib:3.4.0'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
    androidTestImplementation 'androidx.test.espresso:espresso-intents:3.4.0'
    androidTestImplementation 'com.android.support.test.uiautomator:uiautomator-v18:2.1.2'
    androidTestImplementation 'org.jetbrains.kotlin:kotlin-runtime:1.2.10'
}