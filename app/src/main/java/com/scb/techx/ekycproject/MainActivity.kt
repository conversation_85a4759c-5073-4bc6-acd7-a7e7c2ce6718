package com.scb.techx.ekycproject

import android.content.Context
import android.content.res.Configuration
import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.SwitchCompat
import androidx.core.content.res.ResourcesCompat
import com.scb.techx.ekycframework.domain.ocrliveness.model.response.UserConfirmedValue
import com.scb.techx.ekycframework.ui.reviewconfirm.model.DopaResult
import com.scb.techx.ekycframework.ui.theme.*
import com.scb.techx.ekycframework.domain.common.usecase.GetDeviceSettingUseCase
import com.scb.techx.ekycframework.ui.model.ocrprefill.OcrPrefill
import com.scb.techx.ekycframework.util.EkycUtilities
import java.util.Locale

class MainActivity : AppCompatActivity() {

    val ekycUtilities = EkycUtilities()
    private var language = Locale.getDefault().language

    private val tvLanguage: TextView by lazy { findViewById(R.id.tv_language) }
    private val btLiveness: Button by lazy { findViewById(R.id.bt_go_to_liveness) }
    private val btLivenessOcr: Button by lazy { findViewById(R.id.bt_go_to_liveness_ocr) }
    private val btNdid: Button by lazy { findViewById(R.id.bt_go_to_ndid) }
    private val btResult: Button by lazy { findViewById(R.id.bt_go_to_result) }
    private val btOcr: Button by lazy { findViewById(R.id.bt_go_to_ocr) }
    private val swLanguage: SwitchCompat by lazy { findViewById(R.id.sw_language) }
    private lateinit var dialog: AlertDialog

    private val devToken =
        "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    private val sitToken =
        "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    private val sitNdidToken =
        "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    private val prodToken =
        "*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    private val uatToken =
        "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
    private val uatNdidToken = "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    private var sessionId = "7e4f518d-f4ae-447a-a6e4-d4143135e7b1"
    private val token by lazy {
        when (BuildConfig.FLAVOR.uppercase(Locale.getDefault())) {
            "SIT" -> sitToken
            "UAT" -> uatToken
            "DEV" -> devToken
            "PROD" -> prodToken
            "PREPROD" -> "eyJraWQiOiJzY2J0ZWNoeCIsInR5cCI6IkpXVCIsImFsZyI6IlJTMjU2In0.*******************************************************************************************************************************************************************************************************.OgFh-d0bFSOpADm99iiMeFbUNdU8GFeupewBeY8irYaqPYacGlUxDwfAUKj4W30txBZceQpc7KEfXnuaENYxpnvNFSyER2NZKqG40bLNy9p3hz-qeLCtdm_Lw5T0DLCn9oUdi0O_-oDkqeYYHnPDp8Q9FJEBK5zZAMZ7vjzrf1aPJ3SKvJD6CF8_5aaFyXckc-9WK0JaHgrBYTWoh3FHVI_MpXD2MDtZRgUTn8ssfpAxHw6gTLkTXejhwTBfmTMECWVgBcaNt72qkp8snf8bMfOmTbgDRllwT5u8ZrcGY7OYUD5_QTVv0jkf2oOi6T0DUIS2N4isssvdQTje-cJlLHxrS76FjDpazx4gAE4iFQkG1DpgQIhey041mMfq1PYmL2sTXAuKTvZkW5aYe3t9Y84grrqGqKSZx5udUCpJOtECkINHShxR4id3atMemCF6zysH2F0JMJPf0U0245HR2ztNQv6Ux3JBMJw2QORGH3vfzbCxf1_HbpRnoQN5ZA0iSK58tpuRgKQ8_2bm5k7XsLns3p0plyghCfPOIXURI7tls2xKbv8ztvYfck0SY3fTVRbB88z1CQ2MtSAUj0Z2Dd1PB2psT3PiF7qaoNwX-JFaWLHhYktPHhd9LDHUqzwOnNX5_IuyJzido5AZGw6vACBOgZP9chuekmvVc6KeUXE"
            else -> ""
        }
    }

    private var cid = "1309913659936"
    private var ndidResult: NdidCallbackResult? = null
    private var ocrVerifyByFaceResult: OcrIdCardVerifyByFaceResult? = null
    private var resultType: ResultType? = null

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        btLiveness.text = getString(R.string.go_to_liveness)
        btNdid.text = getString(R.string.go_to_ndid)
        btOcr.text = getString(R.string.go_to_ocr)
        btLivenessOcr.text = getString(R.string.go_to_liveness_ocr)
        btResult.text = getString(R.string.go_to_result)
    }

    fun Context.toast(message: CharSequence) =
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()

    private fun popupCIDDialog(
        onClickSubmit: () -> Unit
    ) {
        val mBuilder = AlertDialog.Builder(this@MainActivity)
        val mView: View = layoutInflater.inflate(R.layout.dialog_cid, null)
        val etCIDPopupField: EditText = mView.findViewById(R.id.et_cid_popup_field)
        val btCidSubmit: Button = mView.findViewById(R.id.bt_cid_submit)

        mBuilder.setView(mView)
        dialog = mBuilder.create()
        dialog.show()

        etCIDPopupField.setText("")


        btCidSubmit.setOnClickListener {
            if (etCIDPopupField.text.toString().isNotEmpty()) {
                cid = etCIDPopupField.text.toString()
                dialog.dismiss()
                onClickSubmit()
            } else {
                cid = "1309913659936"
                dialog.dismiss()
                onClickSubmit()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        val SCB_PRIMALY_COLOR = "#ff991f"
        val BLACK_COLOR = "#000000"
        val WHITE_COLOR = "#FFFFFF"
        val PURPLE_COLOR = "#401b9c"
        val RED_COLOR = "#FE0000"
        val BLUE_COLOR = "#0000FE"
        val GREEN_COLOR = "#00FE00"
        val LIGHT_BLUE = "#D1D1FF"
        val GREY_COLOR = "#D1D1D1"

        btOcr.isEnabled = true

        val textTheme = Text(
            fontName = ResourcesCompat.getFont(this, R.font.thsarabunfamily),
            primaryTextColor = "#8005FB00",
            secondaryTextColor = "#072EFB",
            linkColor = "#D51BE3"
        )

        val ocr = Ocr(
            mainHeaderTextColor = SCB_PRIMALY_COLOR,
            sectionHeaderTextColor = BLUE_COLOR,
            fieldLabelTextColor = PURPLE_COLOR
        )

        val button = Button(
            normalTextColor = "#008000",
            normalBackgroundColor = "#D3D3D3",
            outlineBackgroundColor = "#D3D3D3",
            outlineBorderColor = "#FF2600",
            outlineTextColor = "#FF2600"
        )

        val border = Border(
            borderColor = "#05FB00",
            selectedBorderColor = "#072EFB"
        )

        val ndid = Ndid(
            successIcon = R.drawable.camera,
            timerColor = "#FFA500",
            timerBackgroundColor = "#90EE90"
        )

        val customizeTheme = CustomizeTheme(
            text = textTheme,
            button = button,
            oval = null,
            ocr = ocr,
            image = null,
            ndid = ndid,
            border = border,
            other = null
        )

        swLanguage.isChecked = Locale.getDefault().language.equals("th", true)

        if (swLanguage.isChecked) {
            tvLanguage.text = "TH"
        } else {
            tvLanguage.text = "EN"
        }

        swLanguage.setOnCheckedChangeListener { _, isChecked ->
            var locale = Locale(Locale.getDefault().language.lowercase())
            val config = this.resources.configuration
            if (isChecked) {
                tvLanguage.text = "TH"
                language = "TH"
                locale = Locale("th")
            } else {
                tvLanguage.text = "EN"
                language = "EN"
                locale = Locale("en")
            }
            config.setLocale(locale)
            this.resources.updateConfiguration(config, this.resources.displayMetrics)
            onConfigurationChanged(config)
        }

        btLivenessOcr.setOnClickListener {
            sessionId = GetDeviceSettingUseCase.getUUID()
            this.toast(sessionId)
            ekycUtilities.initEkyc(
                context = this,
                sessionId = sessionId,
                token = token,
                environment = BuildConfig.FLAVOR.uppercase(Locale.getDefault()),
                customizeTheme = null,
                applicationContext = applicationContext,
                language = language,
                initCallback = object : EkycUtilities.InitCallback {
                    override fun onSuccess(
                        success: Boolean,
                        description: String,
                        ekycToken: String?
                    ) {
                        if (success) {
//                            ReviewInformationEkycActivity.startActivity(
//                                this@MainActivity,
//                                UserConfirmedValueDisplay(
//                                    "1234567890121",
//                                    "นาย",
//                                    "จอห์น ตร.-",
//                                    "",
//                                    "หอบ",
//                                    "MR.",
//                                    "JOHN",
//                                    "",
//                                    "HOB",
//                                    "12 DEC. 2002",
//                                    "12 DEC. 2012",
//                                    "23 AUG. 2023",
//                                    "JC1231234512"
//                                ),
//                                true
//                            )
                            ocrIdCardVerifyByFace()
                        }
                    }
                }
            )
        }
        btLiveness.setOnClickListener {
            sessionId = GetDeviceSettingUseCase.getUUID()
            this.toast(sessionId)
            ekycUtilities.initEkyc(
                context = this,
                sessionId = sessionId,
                token = token,
                environment = BuildConfig.FLAVOR.uppercase(Locale.getDefault()),
                customizeTheme = null,
                applicationContext = applicationContext,
                language = language,
                initCallback = object : EkycUtilities.InitCallback {
                    override fun onSuccess(
                        success: Boolean,
                        description: String,
                        ekycToken: String?
                    ) {
                        if (success) {
                            livenessCheck()
                        }
                    }
                }
            )
        }

        btNdid.setOnClickListener {
            sessionId = GetDeviceSettingUseCase.getUUID()
            this.toast(sessionId)
            ekycUtilities.initEkyc(
                context = this, //Mandatory
                sessionId = sessionId,//Mandatory
                token = token, //Mandatory
                environment = BuildConfig.FLAVOR.uppercase(Locale.getDefault()), //Mandatory
                customizeTheme = null, //Optional
                applicationContext = applicationContext, //Optional, But it will be mandatory if you need to set language
                language = language, //Optional, Default is default language of app
                initCallback = object : EkycUtilities.InitCallback {
                    override fun onSuccess(
                        success: Boolean,
                        description: String,
                        ekycToken: String?
                    ) {
                        if (success) {
                            popupCIDDialog {
                                ndidVerification()
                            }
                        }
                    }
                }
            )
        }

        btOcr.setOnClickListener {
            sessionId = GetDeviceSettingUseCase.getUUID()
            this.toast(sessionId)
            ekycUtilities.initEkyc(
                context = this,
                sessionId = sessionId,
                token = token,
                environment = BuildConfig.FLAVOR.uppercase(Locale.getDefault()),
                customizeTheme = null,
                applicationContext = applicationContext,
                language = language,
                initCallback = object : EkycUtilities.InitCallback {
                    override fun onSuccess(
                        success: Boolean,
                        description: String,
                        ekycToken: String?
                    ) {
                        if (success) {
                            ocrIdCardCheck()
                        }
                    }
                }
            )
        }

        btResult.setOnClickListener {
            if (resultType == ResultType.NDID) {
                ResultActivity.startActivity(this, ndidResult, resultType)
            } else {
                ResultActivity.startActivity(this, ocrVerifyByFaceResult, resultType)
            }
        }
    }

    private fun livenessCheck() {
        ekycUtilities.livenessCheck(
            context = this,
            livenessCheckCallback = object : EkycUtilities.LivenessCheckCallback {
                override fun onResult(success: Boolean, description: String) {
                    resultType = null
                    resultType = ResultType.OCR
                    ocrVerifyByFaceResult = OcrIdCardVerifyByFaceResult(
                        success = success,
                        description = description,
                        userOcrValue = null,
                        userConfirmedValue = null,
                        dopaResult = null
                    )
                }
            })
    }

    private fun ocrIdCardCheck() {
        ekycUtilities.ocrIdCard(
            context = this@MainActivity,
            checkExpiredIdCard = true,
            checkDopa = false,
            callback = object : EkycUtilities.OCRResultsCallback {
                override fun onSuccess(
                    success: Boolean,
                    description: String,
                    userOcrValue: UserConfirmedValue?,
                    userConfirmedValue: UserConfirmedValue?,
                    dopaResult: DopaResult?
                ) {
                    resultType = null
                    // TODO: Save result once required by QA
                }
            }
        )
    }

    private fun ocrIdCardVerifyByFace() {
        ekycUtilities.ocrIdCardVerifyByFace(
            context = this, //Mandatory
            checkExpiredIdCard = true, //Optional
            enableConfirmInfo = true,
            checkDopa = true,
            ocrPrefill = OcrPrefill(
                titleThFlag = false,
                titleEnFlag = false,
                firstNameThFlag = false,
                firstNameEnFlag = false,
                lastNameEnFlag = false,
                lastNameThFlag = false,
                dateOfBirthFlag = false,
                dateOfIssueFlag = false,
                dateOfExpiryFlag = false,
                laserIdFlag = false
            ),
            ocrResultsCallback = object : EkycUtilities.OCRResultsCallback {
                override fun onSuccess(
                    success: Boolean,
                    description: String,
                    userOcrValue: UserConfirmedValue?,
                    userConfirmedValue: UserConfirmedValue?,
                    dopaResult: DopaResult?
                ) {
                    resultType = ResultType.OCR
                    ocrVerifyByFaceResult = OcrIdCardVerifyByFaceResult(
                        success = success,
                        description = description,
                        userOcrValue = userOcrValue,
                        userConfirmedValue = userConfirmedValue,
                        dopaResult = dopaResult
                    )
                }
            }
        )
    }

    fun ndidVerification() {
        ekycUtilities.ndidVerification(
            context = this,
            identifierType = "national_id",
            identifierValue = cid,
            serviceId = "001.cust_info_001",
            object : EkycUtilities
            .NdidVerificationCallback {
                override fun onResult(
                    success: Boolean,
                    description: String,
                    ndidStatus: String?,
                    ndidError: EkycUtilities.NdidError?,
                    ndidData: EkycUtilities.NdidData?
                ) {
                    resultType = ResultType.NDID
                    ndidResult = NdidCallbackResult(
                        success = success,
                        description = description,
                        ndidStatus = ndidStatus,
                        ndidError = ndidError?.code
                    )
                }
            })
    }
}